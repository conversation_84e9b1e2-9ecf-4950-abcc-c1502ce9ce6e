<?php

namespace addons\shopro\library;

use addons\shopro\service\Wallet;
use app\admin\model\shopro\order\Order;
use app\admin\model\shopro\trade\Order as TradeOrder;
use app\admin\model\shopro\user\User as UserModel;
use app\admin\model\shopro\user\WalletLog;
use app\common\model\User;
use think\Db;
use think\Log;

/**
 * 分润系统核心类
 * 实现用户等级升级和分润计算分配功能
 */
class Bonus
{
    // 分润类型常量
    const BONUS_DIRECT_SALES = 'direct_sales';    // 直接销售
    const BONUS_TEAM = 'team_bonus';              // 团队奖励
    const BONUS_LEVEL = 'level_bonus';            // 岗位补助
    const BONUS_PING = 'ping_bonus';              // 同岗补助

    /**
     * 获取所有用户等级配置
     * @return array
     */
    public static function getUserLevels()
    {
        static $levels = null;

        if ($levels === null) {
            $levels = Db::name('user_group')
                ->where('status', 'normal')
                ->order('level_order', 'asc')
                ->select();

            // 转换为以level_code为键的数组
            $levelMap = [];
            foreach ($levels as $level) {
                $level['upgrade_condition'] = json_decode($level['upgrade_condition'], true) ?: [];
                $level['special_bonus'] = json_decode($level['special_bonus'], true) ?: [];
                $levelMap[$level['level_code']] = $level;
            }
            $levels = $levelMap;
        }

        return $levels;
    }

    /**
     * 根据level_code获取等级配置
     * @param string $level_code
     * @return array|null
     */
    public static function getLevelConfig($level_code)
    {
        $levels = self::getUserLevels();
        return $levels[$level_code] ?? null;
    }

    /**
     * 根据level_order获取等级配置
     * @param int $level_order
     * @return array|null
     */
    public static function getLevelConfigByOrder($level_order)
    {
        $levels = self::getUserLevels();
        foreach ($levels as $level) {
            if ($level['level_order'] == $level_order) {
                return $level;
            }
        }
        return null;
    }

    /**
     * 获取业务配置
     * @return array
     */
    public static function getBusinessConfig()
    {
        static $config = null;

        if ($config === null) {
            // 从数据库获取配置
            $configData = Db::name('shopro_config')
                ->where('parent_code', 'bonus_system')
                ->column('value', 'code');

            $config = [
                'repurchase_multiplier' => intval($configData['repurchase_multiplier'] ?? 10),
                'inactive_days' => intval($configData['inactive_days'] ?? 30),
                'inactive_bonus' => intval($configData['inactive_bonus'] ?? 799),
            ];
        }

        return $config;
    }

    /**
     * 处理订单分润（主入口方法）
     * @param int $order_id 订单ID
     * @param float $unit_price 单盒价格（可选，用于计算销售数量）
     * @return bool
     */
    public static function processOrderBonus($order_id, $unit_price = null)
    {
        try {
            Db::startTrans();

            $order = Order::get($order_id);
            if (!$order || $order->status !== 'paid') {
                Log::error("订单不存在或未支付: {$order_id}");
                return false;
            }

            $user = User::get($order->user_id);
            if (!$user) {
                Log::error("用户不存在: {$order->user_id}");
                return false;
            }

            // 计算奖金商品的总金额
            $bonus_amount = self::calculateBonusAmount($order_id);

            if ($bonus_amount <= 0) {
                Log::info("订单{$order_id}中没有奖金商品或奖金商品金额为0，跳过分润处理");
                Db::commit();
                return true;
            }

            // 计算销售数量（用于统计，不影响分润金额）
            if ($unit_price && $unit_price > 0) {
                $sales_count = floor($bonus_amount / $unit_price);
                $actual_unit_price = $unit_price;
            } else {
                $sales_count = 1;
                $actual_unit_price = $bonus_amount;
            }

            if ($sales_count <= 0) {
                $sales_count = 1; // 至少算作1次销售
            }

            // 获取用户当前等级配置
            $userLevel = self::getUserLevelByUser($user);
            if (!$userLevel) {
                Log::error("用户等级配置不存在: user_id={$user->id}");
                return false;
            }

            // 1. 给购买者发放直接销售奖励（按奖金商品金额百分比）
            self::giveDirectSalesBonus($user, $userLevel, $bonus_amount, $order_id);

            // 2. 更新用户销售统计
            self::updateUserSalesStats($user->id, $sales_count, $actual_unit_price);

            // 3. 分配团队奖励给上级（按奖金商品金额百分比）
            self::distributeTeamBonus($user, $bonus_amount, $order_id);

            // 4. 检查并处理用户等级升级
            self::checkAndUpgradeUserLevel($user->id);

            // 5. 检查复购要求
            self::checkRepurchaseRequirement($user->id, $actual_unit_price);

            Db::commit();
            Log::info("订单{$order_id}分润处理完成，奖金商品金额: {$bonus_amount}元");
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            Log::error("处理订单分润失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 计算订单中奖金商品的总金额
     * @param int $order_id 订单ID
     * @return float
     */
    private static function calculateBonusAmount($order_id)
    {
        // 查询订单中的所有订单项
        $order_items = Db::name('shopro_order_item')
            ->where('order_id', $order_id)
            ->select();

        $bonus_amount = 0;
        $bonus_items = [];

        foreach ($order_items as $item) {
            // 查询商品是否为奖金商品
            $goods = Db::name('shopro_goods')
                ->where('id', $item['goods_id'])
                ->find();

            if ($goods && $goods['is_bonus'] == 1) {
                $bonus_amount += $item['pay_fee'];
                $bonus_items[] = [
                    'goods_id' => $item['goods_id'],
                    'goods_title' => $goods['title'],
                    'pay_fee' => $item['pay_fee']
                ];
            }
        }

        Log::info("订单{$order_id}奖金商品金额计算: {$bonus_amount}元，奖金商品数量: " . count($bonus_items));

        return floatval($bonus_amount);
    }

    /**
     * 获取订单中奖金商品的详细信息
     * @param int $order_id 订单ID
     * @return array
     */
    private static function getBonusItems($order_id)
    {
        // 查询订单中的所有订单项
        $order_items = Db::name('shopro_order_item')
            ->where('order_id', $order_id)
            ->select();

        $bonus_items = [];

        foreach ($order_items as $item) {
            // 查询商品信息
            $goods = Db::name('shopro_goods')
                ->where('id', $item['goods_id'])
                ->find();

            if ($goods && $goods['is_bonus'] == 1) {
                $bonus_items[] = array_merge($item, [
                    'goods_title' => $goods['title'],
                    'is_bonus' => $goods['is_bonus']
                ]);
            }
        }

        return $bonus_items;
    }

    /**
     * 根据用户获取等级配置
     * @param object $user 用户对象
     * @return array|null
     */
    private static function getUserLevelByUser($user)
    {
        // 如果用户有group_id，优先使用group_id对应的等级
        if ($user->group_id) {
            $level = Db::name('user_group')->where('id', $user->group_id)->find();
            if ($level) {
                $level['upgrade_condition'] = json_decode($level['upgrade_condition'], true) ?: [];
                $level['special_bonus'] = json_decode($level['special_bonus'], true) ?: [];
                return $level;
            }
        }

        // 否则根据user_level字段获取默认等级
        return self::getLevelConfigByOrder($user->user_level ?? 1);
    }

    /**
     * 发放直接销售奖励
     * @param object $user 用户对象
     * @param array $userLevel 用户等级配置
     * @param float $order_amount 订单金额
     * @param int $order_id 订单ID
     */
    private static function giveDirectSalesBonus($user, $userLevel, $order_amount, $order_id)
    {
        // 按百分比计算奖励金额
        $cash_bonus = bcmul($order_amount, $userLevel['direct_sales_bonus_rate'], 2);
        $score_bonus = bcmul($order_amount, $userLevel['direct_sales_score_rate'], 2);

        // 发放现金奖励
        if ($cash_bonus > 0) {
            Wallet::change($user->id, 'money', $cash_bonus, 'order_buy_bonus',
                ['order_id' => $order_id], "直接销售奖励 " . ($userLevel['direct_sales_bonus_rate'] * 100) . "%");
        }

        // 发放积分奖励
        if ($score_bonus > 0) {
            Wallet::change($user->id, 'score', $score_bonus, 'order_buy_bonus',
                ['order_id' => $order_id], "直接销售积分奖励 " . ($userLevel['direct_sales_score_rate'] * 100) . "%");
        }

        // 记录分润日志
        self::logBonusRecord($order_id, $user->id, $user->id, self::BONUS_DIRECT_SALES,
            $cash_bonus, $score_bonus, '直接销售奖励');
    }

    /**
     * 分配团队奖励给上级
     * @param object $user 购买用户
     * @param float $order_amount 订单金额
     * @param int $order_id 订单ID
     */
    private static function distributeTeamBonus($user, $order_amount, $order_id)
    {
        $current_user = $user;
        $levels = self::getUserLevels();

        // 向上查找上级用户，按等级发放团队奖励
        while ($current_user->parent_user_id) {
            $parent_user = User::get($current_user->parent_user_id);
            if (!$parent_user) {
                break;
            }

            // 获取上级用户的等级配置
            $parentLevel = self::getUserLevelByUser($parent_user);
            if (!$parentLevel || $parentLevel['team_bonus_rate'] <= 0) {
                $current_user = $parent_user;
                continue;
            }

            // 按百分比计算团队奖励金额
            $team_bonus = bcmul($order_amount, $parentLevel['team_bonus_rate'], 2);
            $score_bonus = bcmul($order_amount, $parentLevel['direct_sales_score_rate'], 2);

            // 发放团队奖励
            if ($team_bonus > 0) {
                Wallet::change($parent_user->id, 'money', $team_bonus, 'order_invite_bonus',
                    ['order_id' => $order_id], $parentLevel['name'] . "团队奖励 " . ($parentLevel['team_bonus_rate'] * 100) . "%");
            }

            if ($score_bonus > 0) {
                Wallet::change($parent_user->id, 'score', $score_bonus, 'order_invite_bonus',
                    ['order_id' => $order_id], $parentLevel['name'] . "团队积分奖励 " . ($parentLevel['direct_sales_score_rate'] * 100) . "%");
            }

            // 记录分润日志
            self::logBonusRecord($order_id, $user->id, $parent_user->id, self::BONUS_TEAM,
                $team_bonus, $score_bonus, $parentLevel['name'] . '团队奖励');

            // 处理特殊奖励
            if (!empty($parentLevel['special_bonus'])) {
                self::processSpecialBonus($parent_user, $parentLevel['special_bonus'], $order_amount, $order_id, $user->id);
            }

            $current_user = $parent_user;
        }
    }

    /**
     * 处理特殊奖励
     * @param object $user 获得奖励的用户
     * @param array $specialBonus 特殊奖励配置
     * @param float $order_amount 订单金额
     * @param int $order_id 订单ID
     * @param int $buyer_user_id 购买用户ID
     */
    private static function processSpecialBonus($user, $specialBonus, $order_amount, $order_id, $buyer_user_id)
    {
        // 智慧董事特殊补助：按订单金额百分比额外积分
        if (isset($specialBonus['extra_score_rate']) && $specialBonus['extra_score_rate'] > 0) {
            $extra_score = bcmul($order_amount, $specialBonus['extra_score_rate'], 2);
            Wallet::change($user->id, 'score', $extra_score, 'order_level_bonus',
                ['order_id' => $order_id], "特殊补助 " . ($specialBonus['extra_score_rate'] * 100) . "%");

            self::logBonusRecord($order_id, $buyer_user_id, $user->id, self::BONUS_LEVEL,
                0, $extra_score, '特殊积分补助');
        }
    }

    /**
     * 更新用户销售统计
     * @param int $user_id 用户ID
     * @param int $sales_count 销售数量
     * @param float $unit_price 单价
     */
    private static function updateUserSalesStats($user_id, $sales_count, $unit_price)
    {
        $user = User::get($user_id);
        if (!$user) return;

        // 更新个人销售统计
        $user->sales_count = $user->sales_count + $sales_count;
        $user->last_sale_time = time();
        $user->is_active = 1;

        // 更新累计销售金额
        $sales_amount = bcmul($sales_count, $unit_price, 2);
        $user->total_consume = bcadd($user->total_consume, $sales_amount, 2);
        $user->save();

        // 更新上级的团队销售统计
        $current_user = $user;
        while ($current_user->parent_user_id) {
            $parent_user = User::get($current_user->parent_user_id);
            if (!$parent_user) break;

            $parent_user->team_sales_count = $parent_user->team_sales_count + $sales_count;
            $parent_user->save();

            $current_user = $parent_user;
        }
    }

    /**
     * 检查并处理用户等级升级
     * @param int $user_id 用户ID
     */
    public static function checkAndUpgradeUserLevel($user_id)
    {
        $user = User::get($user_id);
        if (!$user) return;

        $currentLevel = self::getUserLevelByUser($user);
        if (!$currentLevel) return;

        $levels = self::getUserLevels();
        $nextLevelOrder = $currentLevel['level_order'] + 1;
        $nextLevel = self::getLevelConfigByOrder($nextLevelOrder);

        if (!$nextLevel) return; // 已经是最高等级

        // 检查升级条件
        if (self::checkUpgradeCondition($user, $nextLevel['upgrade_condition'])) {
            // 检查名额限制
            if ($nextLevel['max_count'] > 0) {
                $currentCount = Db::name('user_group')
                    ->alias('ug')
                    ->join('user u', 'u.group_id = ug.id')
                    ->where('ug.level_code', $nextLevel['level_code'])
                    ->count();

                if ($currentCount >= $nextLevel['max_count']) {
                    Log::info("用户{$user_id}升级失败：{$nextLevel['name']}名额已满");
                    return;
                }
            }

            // 执行升级
            self::upgradeUserLevel($user_id, $currentLevel, $nextLevel);

            // 递归检查是否可以继续升级
            self::checkAndUpgradeUserLevel($user_id);
        }
    }

    /**
     * 检查升级条件
     * @param object $user 用户对象
     * @param array $condition 升级条件
     * @return bool
     */
    private static function checkUpgradeCondition($user, $condition)
    {
        if (empty($condition['type'])) return false;

        switch ($condition['type']) {
            case 'purchase':
                // 购买条件：检查累计消费金额
                return $user->total_consume >= ($condition['amount'] ?? 0);

            case 'personal_sales':
                // 个人销售条件
                return $user->sales_count >= ($condition['count'] ?? 0);

            case 'team_level':
                // 团队等级条件：团队中指定等级的人数
                $targetLevel = self::getLevelConfig($condition['level_code']);
                if (!$targetLevel) return false;

                $count = self::getTeamLevelCount($user->id, $targetLevel['level_order']);
                return $count >= ($condition['count'] ?? 0);

            default:
                return false;
        }
    }

    /**
     * 执行用户等级升级
     * @param int $user_id 用户ID
     * @param array $old_level 原等级配置
     * @param array $new_level 新等级配置
     */
    private static function upgradeUserLevel($user_id, $old_level, $new_level)
    {
        $user = User::get($user_id);
        if (!$user) return;

        // 更新用户等级和组别
        $user->user_level = $new_level['level_order'];
        $user->group_id = $new_level['id'];
        $user->save();

        // 记录升级日志
        $team_stats = self::getTeamStats($user_id);
        self::logLevelUpgrade($user_id, $old_level['level_order'], $new_level['level_order'],
            "满足升级条件自动升级", json_encode($team_stats));

        Log::info("用户等级升级: 用户{$user_id} 从 {$old_level['name']} 升级到 {$new_level['name']}");
    }

    /**
     * 获取团队中指定等级的人数
     * @param int $user_id 用户ID
     * @param int $level_order 等级排序
     * @return int
     */
    private static function getTeamLevelCount($user_id, $level_order)
    {
        // 递归查询所有下级用户中指定等级的人数
        $sql = "
            WITH RECURSIVE team_tree AS (
                SELECT id, parent_user_id, user_level, 1 as depth
                FROM sys_user
                WHERE parent_user_id = :user_id

                UNION ALL

                SELECT u.id, u.parent_user_id, u.user_level, t.depth + 1
                FROM sys_user u
                INNER JOIN team_tree t ON u.parent_user_id = t.id
                WHERE t.depth < 10
            )
            SELECT COUNT(*) as count
            FROM team_tree
            WHERE user_level = :level_order
        ";

        $result = Db::query($sql, ['user_id' => $user_id, 'level_order' => $level_order]);
        return $result[0]['count'] ?? 0;
    }

    /**
     * 获取团队统计数据
     * @param int $user_id 用户ID
     * @return array
     */
    private static function getTeamStats($user_id)
    {
        return [
            'direct_subordinates' => User::where('parent_user_id', $user_id)->count(),
            'level_2_count' => self::getTeamLevelCount($user_id, self::LEVEL_ADVISOR),
            'level_3_count' => self::getTeamLevelCount($user_id, self::LEVEL_MANAGER),
            'level_4_count' => self::getTeamLevelCount($user_id, self::LEVEL_DIRECTOR),
            'level_5_count' => self::getTeamLevelCount($user_id, self::LEVEL_WISDOM),
        ];
    }

    /**
     * 检查是否有资格升级为享业董事（名额限制）
     * @return bool
     */
    private static function isEligibleForDirectorLevel()
    {
        $config = self::getBusinessConfig();
        $current_directors = User::where('user_level', self::LEVEL_DIRECTOR)->count();
        return $current_directors < $config['director_limit'];
    }

    /**
     * 检查复购要求
     * @param int $user_id 用户ID
     * @param float $unit_price 单价（可选）
     */
    public static function checkRepurchaseRequirement($user_id, $unit_price = null)
    {
        $user = User::get($user_id);
        if (!$user) return;

        $config = self::getBusinessConfig();

        // 计算用户累计销售金额（使用total_consume字段）
        $total_sales_amount = $user->total_consume;

        // 计算用户累计获得分润
        $bonusStats = self::getUserBonusStats($user_id);
        $total_bonus = $bonusStats['total_bonus'];

        // 检查是否达到复购要求（分润达到销售金额的倍数）
        $repurchase_threshold = bcmul($total_sales_amount, $config['repurchase_multiplier'], 2);

        if (bccomp($total_bonus, $repurchase_threshold, 2) >= 0) {
            $user->repurchase_required = 1;
            $user->total_bonus_received = $total_bonus; // 更新累计分润字段
            $user->save();

            Log::info("用户{$user_id}需要复购：累计分润{$total_bonus}，销售金额{$total_sales_amount}");
        }
    }

    /**
     * 处理非活跃用户出局
     * 定时任务调用，处理1个月内无销售的用户
     */
    public static function handleInactiveUsers()
    {
        $config = self::getBusinessConfig();
        $inactive_timestamp = time() - ($config['inactive_days'] * 24 * 3600);

        // 查找非活跃用户（1个月内无销售且已激活的用户）
        $inactive_users = User::where('is_active', 1)
            ->where('last_sale_time', '<', $inactive_timestamp)
            ->where('last_sale_time', '>', 0)
            ->select();

        foreach ($inactive_users as $user) {
            try {
                Db::startTrans();

                // 按购买顺序一次性补助积分后出局
                $bonus_score = $config['inactive_bonus'];
                Wallet::change($user->id, 'score', $bonus_score, 'activity_gift',
                    [], '非活跃用户出局补助');

                // 设置为非激活状态
                $user->is_active = 0;
                $user->save();

                Log::info("用户{$user->id}因非活跃出局，补助{$bonus_score}积分");

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                Log::error("处理非活跃用户{$user->id}出局失败: " . $e->getMessage());
            }
        }
    }

    /**
     * 记录分润日志
     * @param int $order_id 订单ID
     * @param int $user_id 购买用户ID
     * @param int $bonus_user_id 获得分润的用户ID
     * @param string $bonus_type 分润类型
     * @param float $amount 分润金额
     * @param float $score_amount 积分金额
     * @param string $memo 备注
     */
    private static function logBonusRecord($order_id, $user_id, $bonus_user_id, $bonus_type, $amount, $score_amount, $memo)
    {
        Db::name('shopro_bonus_log')->insert([
            'order_id' => $order_id,
            'user_id' => $user_id,
            'bonus_user_id' => $bonus_user_id,
            'bonus_type' => $bonus_type,
            'amount' => $amount,
            'score_amount' => $score_amount,
            'memo' => $memo,
            'createtime' => time()
        ]);
    }

    /**
     * 记录等级升级日志
     * @param int $user_id 用户ID
     * @param int $old_level 原等级
     * @param int $new_level 新等级
     * @param string $reason 升级原因
     * @param string $team_stats 团队统计数据
     */
    private static function logLevelUpgrade($user_id, $old_level, $new_level, $reason, $team_stats)
    {
        Db::name('shopro_user_level_log')->insert([
            'user_id' => $user_id,
            'old_level' => $old_level,
            'new_level' => $new_level,
            'upgrade_reason' => $reason,
            'team_stats' => $team_stats,
            'createtime' => time()
        ]);
    }

    /**
     * 获取用户等级名称
     * @param int $level_order 等级排序
     * @return string
     */
    public static function getLevelName($level_order)
    {
        $level = self::getLevelConfigByOrder($level_order);
        return $level ? $level['name'] : '未知等级';
    }

    /**
     * 获取用户分润统计
     * @param int $user_id 用户ID
     * @return array
     */
    public static function getUserBonusStats($user_id)
    {
        $total_bonus = Db::name('shopro_bonus_log')
            ->where('bonus_user_id', $user_id)
            ->sum('amount');

        $total_score = Db::name('shopro_bonus_log')
            ->where('bonus_user_id', $user_id)
            ->sum('score_amount');

        return [
            'total_bonus' => $total_bonus ?: 0,
            'total_score' => $total_score ?: 0
        ];
    }
}